import { config } from './data_config.js';

document.addEventListener('DOMContentLoaded', () => {
    lucide.createIcons();
    const form = document.getElementById('cost-estimator-form');
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        calculateAndDisplayCosts();
    });


    document.getElementById('actualWeight').value = 10;
    document.getElementById('length').value = 50;
    document.getElementById('width').value = 40;
    document.getElementById('height').value = 30;
    document.getElementById('declaredValue').value = 500;
    document.getElementById('storageDays').value = 100;
    document.getElementById('skusPerOrder').value = 1;

    calculateAndDisplayCosts();
});

function getFormValues() {
    const form = document.getElementById('cost-estimator-form');
    const formData = new FormData(form);
    const values = {};
    for (let [key, value] of formData.entries()) {
        values[key] = value;
    }

    values.requiresInsurance = form.querySelector('#requiresInsurance').checked;
    values.requiresExportRebate = form.querySelector('#requiresExportRebate').checked;
    

    const numericFields = ['actualWeight', 'length', 'width', 'height', 'declaredValue', 'storageDays', 'skusPerOrder', 'vasRelabel', 'vasRepackage'];
    numericFields.forEach(field => {
        values[field] = parseFloat(values[field]) || 0;
    });

    return values;
}

function formatCurrency(amount) {
    if (typeof amount === 'string') {
        return amount;
    }
    return `¥ ${amount.toFixed(2)}`;
}

function calculateAndDisplayCosts() {
    const inputs = getFormValues();
    const notes = [];


    const volumetricWeight = (inputs.length * inputs.width * inputs.height) / config.VOLUMETRIC_DIVISOR;
    const chargeableWeight = Math.max(inputs.actualWeight, volumetricWeight);
    notes.push({ type: 'info', text: `计费重量: ${chargeableWeight.toFixed(2)} kg (实重 ${inputs.actualWeight} kg, 体积重 ${volumetricWeight.toFixed(2)} kg)` });


    const firstMileResult = calculateFirstMileCost(inputs, chargeableWeight);
    const storageResult = calculateStorageCost(inputs);
    const lastMileResult = calculateLastMileCost(inputs);
    const vasResult = calculateWmsVasCost(inputs);
    
    notes.push(...firstMileResult.notes, ...storageResult.notes, ...lastMileResult.notes, ...vasResult.notes);

    const totalCost = firstMileResult.cost + storageResult.cost + lastMileResult.cost + vasResult.cost;


    document.getElementById('total-cost').textContent = formatCurrency(totalCost);
    document.getElementById('first-mile-cost').textContent = formatCurrency(firstMileResult.cost);
    document.getElementById('storage-cost').textContent = formatCurrency(storageResult.cost);
    document.getElementById('last-mile-cost').textContent = formatCurrency(lastMileResult.cost);
    document.getElementById('vas-cost').textContent = formatCurrency(vasResult.cost);

    const notesContainer = document.getElementById('notes-container');
    notesContainer.innerHTML = '';
    notes.forEach(note => {
        const p = document.createElement('p');
        p.innerHTML = `<i data-lucide="${note.type === 'warning' ? 'alert-triangle' : 'info'}"></i> ${note.text}`;
        notesContainer.appendChild(p);
    });
    lucide.createIcons();
}

function calculateFirstMileCost(inputs, chargeableWeight) {
    const { firstMileServiceType, declaredValue, requiresInsurance, requiresExportRebate } = inputs;
    const rateInfo = config.firstMileRates[firstMileServiceType];
    const notes = [];
    let cost = 0;

    if (!rateInfo) return { cost: 0, notes: [{ type: 'warning', text: '无效的头程服务类型' }] };

    if (rateInfo.quote) {
        notes.push({ type: 'warning', text: `${rateInfo.name}: 需单独报价` });
        return { cost: 0, notes };
    }
    
    let baseWeight = chargeableWeight;
    if (rateInfo.minWeight && chargeableWeight < rateInfo.minWeight) {
        baseWeight = rateInfo.minWeight;
        notes.push({ type: 'info', text: `${rateInfo.name}最低 ${rateInfo.minWeight}KG 起运，已按最低重量计费。` });
    }

    const transportCost = baseWeight * (rateInfo.baseRate + rateInfo.surcharge);
    cost += transportCost;

    if (requiresInsurance) {
        const insuranceFee = declaredValue * config.INSURANCE_RATE;
        cost += insuranceFee;
        notes.push({ type: 'info', text: `保险费: ${formatCurrency(insuranceFee)}` });
    }

    if (requiresExportRebate) {
        cost += config.EXPORT_REBATE_FEE;
        notes.push({ type: 'info', text: `出口退税文件费: ${formatCurrency(config.EXPORT_REBATE_FEE)}` });
    }
    
    return { cost, notes };
}

function calculateStorageCost(inputs) {
    const { length, width, height, storageDays } = inputs;
    if (storageDays <= 0) return { cost: 0, notes: [] };

    const volumeCbm = (length * width * height) / 1000000;
    if (volumeCbm <= 0) return { cost: 0, notes: [] };
    
    let remainingDays = storageDays;
    let totalCost = 0;
    const notes = [];

    for (const tier of config.storageRateTiers) {
        if (remainingDays <= 0) break;
        
        const daysInThisTier = Math.min(remainingDays, tier.maxDays - tier.minDays + 1);
        if (daysInThisTier > 0) {
            const tierCost = daysInThisTier * tier.ratePerCbmPerDay * volumeCbm;
            totalCost += tierCost;
            remainingDays -= daysInThisTier;
        }
    }
     if (storageDays > config.FREE_STORAGE_DAYS) {
        notes.push({ type: 'info', text: `仓储费已计算 ${storageDays} 天, 其中前 ${config.FREE_STORAGE_DAYS} 天免费。` });
    } else {
        notes.push({ type: 'info', text: `仓储 ${storageDays} 天，在 ${config.FREE_STORAGE_DAYS} 天免费期内。` });
    }

    return { cost: totalCost, notes };
}

function calculateLastMileCost(inputs) {
    const { actualWeight, skusPerOrder } = inputs;
    if (actualWeight <= 0) return { cost: 0, notes: [] };
    const notes = [];
    

    if (actualWeight > 30) {
        notes.push({ type: 'warning', text: '尾程派送: 货物超过30kg，需单独报价。' });
        return { cost: 0, notes };
    }


    const roundedWeightKg = Math.ceil(actualWeight * 10) / 10;
    notes.push({ type: 'info', text: `尾程派送计费重量(进位后): ${roundedWeightKg} kg` });
    

    const tier = config.lastMileRateTiers.find(t => roundedWeightKg > t.minWeight && roundedWeightKg <= t.maxWeight);
    if (!tier) return { cost: 0, notes: [{ type: 'warning', text: '无法匹配尾程派送重量段' }] };
    
    let deliveryCost = tier.baseFee + (roundedWeightKg * 10 - tier.baseWeightGrams) * tier.ratePer100g + tier.operationFee;


    let multiItemFee = 0;
    const feeTier = config.multiItemHandlingFees.find(t => skusPerOrder >= t.minSkus);
    if (feeTier) {
        multiItemFee = feeTier.fee;
    }
    if(multiItemFee > 0) {
        notes.push({ type: 'info', text: `一票多件处理费 (${skusPerOrder} SKUs): ${formatCurrency(multiItemFee)}` });
    }

    return { cost: deliveryCost + multiItemFee, notes };
}


function calculateWmsVasCost(inputs) {
    const { vasRelabel, vasRepackage } = inputs;
    const notes = [];
    let totalCost = 0;

    const vasItems = {
        relabel: vasRelabel,
        repackage: vasRepackage, // Assuming '换包装' is the same rate as '换标' as per simplified request
    };

    for (const [item, quantity] of Object.entries(vasItems)) {
        if (quantity > 0) {
            const rateInfo = config.wmsVasRates[item];
            if (rateInfo) {
                const itemCost = quantity * rateInfo.price;
                totalCost += itemCost;
                notes.push({type: 'info', text: `增值服务 (${rateInfo.name}): ${formatCurrency(itemCost)}`})
            }
        }
    }
    
    return { cost: totalCost, notes };
}
