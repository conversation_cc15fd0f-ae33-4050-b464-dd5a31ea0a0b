@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

body {
    font-family: 'Noto Sans SC', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#app {
    transition: opacity 0.5s ease-in-out;
}

.form-section-title {
    @apply text-xl font-bold text-slate-800 border-b pb-2 flex items-center;
}

.form-label {
    @apply block text-sm font-medium text-slate-600 mb-1;
}

.form-input {
    @apply block w-full px-3 py-2 bg-slate-50 border border-slate-300 rounded-md text-sm shadow-sm placeholder-slate-400
    focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500
    transition duration-150;
}

.form-checkbox {
    @apply h-5 w-5 rounded border-slate-300 text-blue-600 focus:ring-blue-500 transition duration-150;
}

#calculate-btn {
    box-shadow: 0 4px 14px 0 rgb(0 118 255 / 39%);
}

#results-container {
    transition: all 0.3s ease-in-out;
}

.result-item {
    @apply flex justify-between items-center bg-blue-900/30 p-3 rounded-lg;
}

.result-item span:first-child {
    @apply flex items-center text-blue-200;
}

.icon-sm {
    @apply w-5 h-5 mr-3 text-blue-400;
}

#notes-container p {
    @apply flex items-start;
}

#notes-container i {
    @apply w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-amber-300;
}
