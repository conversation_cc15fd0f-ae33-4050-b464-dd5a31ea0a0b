export const config = {
    VOLUMETRIC_DIVISOR: 8000,
    INSURANCE_RATE: 0.02,
    EXPORT_REBATE_FEE: 1000,
    FREE_STORAGE_DAYS: 90,

    firstMileRates: {
        'ZOBL': { name: '铁运', baseRate: 17, surcharge: 0, minWeight: 100 },
        'PHLY1': { name: '普货陆运', baseRate: 22, surcharge: 8 },
        'MGLY': { name: '敏感货陆运', baseRate: 25, surcharge: 13 },
        'FZLY': { name: '服装陆运', baseRate: 30, surcharge: 10 },
        'XBLY': { name: '箱包陆运', baseRate: 30, surcharge: 8 },
        'KLY': { name: '普货快陆运', baseRate: 25, surcharge: 8 },
        'PHKY-MK': { name: '慢空路线', baseRate: 36, surcharge: 0 },
        'PHKY': { name: '空运', quote: true },
        'SELF_DELIVERY': { name: '客户自行承运头程', baseRate: 10, surcharge: 0 }
    },

    storageRateTiers: [
        { minDays: 0, maxDays: 90, ratePerCbmPerDay: 0 },
        { minDays: 91, maxDays: 180, ratePerCbmPerDay: 6 },
        { minDays: 181, maxDays: 270, ratePerCbmPerDay: 9 },
        { minDays: 271, maxDays: 360, ratePerCbmPerDay: 12 },
        { minDays: 361, maxDays: 450, ratePerCbmPerDay: 30 },
        { minDays: 451, maxDays: 540, ratePerCbmPerDay: 60 },
        { minDays: 541, maxDays: 630, ratePerCbmPerDay: 90 },
        { minDays: 631, maxDays: 720, ratePerCbmPerDay: 120 },
        { minDays: 721, maxDays: Infinity, ratePerCbmPerDay: 150 }
    ],
    
    lastMileRateTiers: [
        { minWeight: 0, maxWeight: 4.9, baseFee: 25, baseWeightGrams: 1, ratePer100g: 1, operationFee: 6.5 },
        { minWeight: 4.9, maxWeight: 9.9, baseFee: 50, baseWeightGrams: 50, ratePer100g: 1, operationFee: 6.5 },
        { minWeight: 9.9, maxWeight: 30, baseFee: 95, baseWeightGrams: 100, ratePer100g: 1, operationFee: 6.5 }
    ],

    multiItemHandlingFees: [
        { minSkus: 5, fee: 5 },
        { minSkus: 4, fee: 4 },
        { minSkus: 3, fee: 3 },
        { minSkus: 2, fee: 2 },
        { minSkus: 1, fee: 0 }
    ],
    
    wmsVasRates: {
        'relabel': { name: '换标', price: 2, unit: '个/SKU' },
        'repackage': { name: '换包装(快递袋)', price: 2, unit: '个/SKU' },
        'reweigh': { name: '核重', price: 2, unit: '个/SKU' },
        'split': { name: '拆分', price: 2, unit: '个/SKU' },

    }
};
